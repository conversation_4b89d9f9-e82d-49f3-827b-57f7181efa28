{"version": 4, "terraform_version": "1.12.1", "serial": 3, "lineage": "d6c811db-8bd0-7487-23ed-68df91bb9302", "outputs": {"ami_id": {"value": "ami-0bf2261cbe476a33d", "type": "string"}, "ami_name": {"value": "ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-20250530", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_ami", "name": "ubuntu", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architecture": "arm64", "arn": "arn:aws-us-gov:ec2:us-gov-west-1::image/ami-0bf2261cbe476a33d", "block_device_mappings": [{"device_name": "/dev/sda1", "ebs": {"delete_on_termination": "true", "encrypted": "false", "iops": "0", "snapshot_id": "snap-0a243533243680758", "throughput": "0", "volume_initialization_rate": "0", "volume_size": "8", "volume_type": "gp3"}, "no_device": "", "virtual_name": ""}, {"device_name": "/dev/sdb", "ebs": {}, "no_device": "", "virtual_name": "ephemeral0"}, {"device_name": "/dev/sdc", "ebs": {}, "no_device": "", "virtual_name": "ephemeral1"}], "boot_mode": "uefi", "creation_date": "2025-05-30T14:43:59.000Z", "deprecation_time": "2027-05-30T14:43:59.000Z", "description": "Canonical, U<PERSON>ntu, 24.04, arm64 noble image", "ena_support": true, "executable_users": null, "filter": [{"name": "architecture", "values": ["arm64"]}, {"name": "name", "values": ["ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-*"]}, {"name": "virtualization-type", "values": ["hvm"]}], "hypervisor": "xen", "id": "ami-0bf2261cbe476a33d", "image_id": "ami-0bf2261cbe476a33d", "image_location": "513442679011/ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-20250530", "image_owner_alias": "", "image_type": "machine", "imds_support": "v2.0", "include_deprecated": false, "kernel_id": "", "last_launched_time": "", "most_recent": true, "name": "ubuntu/images/hvm-ssd-gp3/ubuntu-noble-24.04-arm64-server-20250530", "name_regex": null, "owner_id": "513442679011", "owners": ["513442679011"], "platform": "", "platform_details": "Linux/UNIX", "product_codes": [], "public": true, "ramdisk_id": "", "root_device_name": "/dev/sda1", "root_device_type": "ebs", "root_snapshot_id": "snap-0a243533243680758", "sriov_net_support": "simple", "state": "available", "state_reason": {"code": "UNSET", "message": "UNSET"}, "tags": {}, "timeouts": null, "tpm_support": "", "uefi_data": null, "usage_operation": "RunInstances", "virtualization_type": "hvm"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "managed", "type": "aws_key_pair", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws-us-gov:ec2:us-gov-west-1:554213606819:key-pair/vibes-key", "fingerprint": "6KMbbcc7jy9XkJ0Nnfvsbm1iJiVgVzVpcaDKHfC3B54=", "id": "vibes-key", "key_name": "vibes-key", "key_name_prefix": "", "key_pair_id": "key-0e0f64417f6dcd620", "key_type": "ed25519", "public_key": "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJDCPrj3mTjgRhIkMwWXyyQsoy8q7NG42frqgP0tayjn lee@eLEEtbook", "tags": {"Name": "vibes-key"}, "tags_all": {"Name": "vibes-key"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}], "check_results": null}