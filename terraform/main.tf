# Configure the AWS Provider
provider "aws" {
  region  = var.region
  profile = "astra"
}

# Data source to get the latest Ubuntu 24.04 LTS ARM64 AMI
data "aws_ami" "ubuntu" {
  most_recent = true
  owners      = ["099720109477"] # Canonical

  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-noble-24.04-arm64-server-*"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }

  filter {
    name   = "architecture"
    values = ["arm64"]
  }
}

# Create a key pair using the existing public key
resource "aws_key_pair" "main" {
  key_name   = var.key_name
  public_key = file("../key.pub")

  tags = {
    Name = var.key_name
  }
}

# Create a security group for SSH access
resource "aws_security_group" "ssh" {
  name_prefix = "vibes-ssh-"
  description = "Security group for SSH access"

  ingress {
    description = "SSH"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.allowed_cidr_blocks
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "vibes-ssh-sg"
  }
}

# Create the EC2 instance
resource "aws_instance" "main" {
  ami           = data.aws_ami.ubuntu.id
  instance_type = var.instance_type
  key_name      = aws_key_pair.main.key_name

  vpc_security_group_ids = [aws_security_group.ssh.id]

  root_block_device {
    volume_type = "gp3"
    volume_size = 20
    encrypted   = true
  }

  tags = {
    Name = var.instance_name
  }
}

# Create additional EBS volume
resource "aws_ebs_volume" "additional" {
  availability_zone = aws_instance.main.availability_zone
  size              = var.ebs_volume_size
  type              = "gp3"
  encrypted         = true

  tags = {
    Name = "${var.instance_name}-additional-volume"
  }
}

# Attach the additional EBS volume to the instance
resource "aws_volume_attachment" "additional" {
  device_name = "/dev/sdf"
  volume_id   = aws_ebs_volume.additional.id
  instance_id = aws_instance.main.id
}
