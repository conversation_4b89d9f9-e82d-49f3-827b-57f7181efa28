# EC2 Instance Terraform Configuration

This Terraform configuration creates an r7g.large EC2 instance in the us-gov-west-1 region with the following specifications:

## Resources Created

- **EC2 Instance**: r7g.large instance running Ubuntu 24.04 LTS (ARM64)
- **EBS Volumes**: 
  - Root volume: 20 GB (encrypted)
  - Additional volume: 100 GB (encrypted, attached as /dev/sdf)
- **Security Group**: Allows SSH access on port 22
- **Key Pair**: Uses the public key from `../key.pub`

## Prerequisites

1. **AWS CLI configured** with SSO profile "astra" for AWS GovCloud
2. **Terraform installed** (version >= 1.0)
3. **Active AWS SSO session** with permissions to create EC2 instances, security groups, and EBS volumes

## AWS SSO Authentication

Before running Terraform, ensure you have an active SSO session:

```bash
# Login to AWS SSO (if not already logged in)
aws sso login --profile astra

# Verify your session is active
aws sts get-caller-identity --profile astra
```

## Usage

1. **Initialize Terraform**:
   ```bash
   cd terraform
   terraform init
   ```

2. **Review the plan**:
   ```bash
   terraform plan
   ```

3. **Apply the configuration**:
   ```bash
   terraform apply
   ```

4. **Connect to the instance**:
   After deployment, use the SSH command from the output:
   ```bash
   ssh -i /path/to/your/private/key ubuntu@<public_ip>
   ```

## Customization

You can customize the deployment by modifying variables in `variables.tf` or by passing them during apply:

```bash
terraform apply -var="instance_type=r7g.xlarge" -var="ebs_volume_size=200"
```

## Additional Volume Setup

After connecting to the instance, you'll need to format and mount the additional 100 GB volume:

```bash
# Check available disks
lsblk

# Format the additional volume (usually /dev/nvme1n1 or /dev/xvdf)
sudo mkfs.ext4 /dev/nvme1n1

# Create mount point
sudo mkdir /data

# Mount the volume
sudo mount /dev/nvme1n1 /data

# Add to fstab for persistent mounting
echo '/dev/nvme1n1 /data ext4 defaults 0 2' | sudo tee -a /etc/fstab
```

## Security Notes

- The security group allows SSH access from anywhere (0.0.0.0/0)
- Consider restricting the `allowed_cidr_blocks` variable to your specific IP ranges
- Both EBS volumes are encrypted by default

## Cleanup

To destroy all resources:

```bash
terraform destroy
```
